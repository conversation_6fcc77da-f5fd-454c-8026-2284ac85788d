<?php

namespace App\Services\Machine;

use App\Jobs\MatchEventAndTask;
use App\Models\Integration;
use App\Models\MachineEvent;
use App\Models\Plot;
use App\Models\StaticModels\FarmingYear;
use App\Models\WorkOperation;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Staudenmeir\LaravelCte\Query\Builder;

class MachineEventService
{
    public function storeMachineEventsReport(string $serverName, string $tmpTableName, int $organizationId): void
    {
        DB::beginTransaction();

        try {
            $tmpTableExists = Schema::hasTable($tmpTableName);

            if (!$tmpTableExists) {
                return;
            }

            $query = $this->getContentQuery($tmpTableName, $organizationId);
            MachineEvent::insertMachineEventData($query);
            DB::statement("DROP TABLE IF EXISTS {$tmpTableName}");
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            DB::statement("DROP TABLE IF EXISTS {$tmpTableName}");

            throw $e;
        }

        dispatch(
            new MatchEventAndTask($serverName, $organizationId)
        );
    }

    public function deleteEvents(int $organizationId, string $date, ?int $wialonUnitId): void
    {
        MachineEvent::deleteEvents($organizationId, $date, $wialonUnitId);
    }

    public function detachEventsFromTasks(int $organizationId, string $date, ?int $wialonUnitId): void
    {
        MachineEvent::detachEventsFromTasks($organizationId, $date, $wialonUnitId);
    }

    private function getContentQuery(string $tmpTableName, int $organizationId): Builder
    {
        $queryRawPoints = DB::table("{$tmpTableName} as tmp")->select(
            DB::raw('mu.id as id'),
            'grouping as machine',
            DB::raw('time'),
            'coordinates as geom',
            DB::raw("replace(speed, ' km/h', '')::numeric as speed"),
            DB::raw("case when value='' then null else value::numeric end consumption"),
            DB::raw("case when trailer <> '' and smi.status = 'Active' then trailer else null end trailer"),
            DB::raw("case when driver = '' then null else driver end driver"),
            DB::raw("case when (replace(speed, ' km/h',''))::numeric >1 then 'Move' else 'Stop' end state")
        )
            ->join('su_machine_units as mu', 'mu.wialon_unit_imei', '=', DB::raw('tmp.wialon_unit_imei::bigint'))
            ->join('su_integration as i', function ($join) {
                $join->on('i.id', '=', 'mu.integration_id')
                    ->where('i.status', Integration::ACTIVE);
            })
            // TODO: Change this to join by id when it is available
            ->leftJoin('su_machines_implements as smi', function ($join) use ($organizationId) {
                $join->on('smi.name', '=', 'trailer')
                    ->on('smi.integration_id', 'i.id')
                    ->where('smi.organization_id', $organizationId);
            });

        // Clean points with same TS and state but different locations
        $queryPoints = DB::table('raw_points')->select(
            'id as unit_id',
            DB::raw('st_centroid(st_collect(geom)) as geom'),
            DB::raw('avg(speed) as speed'),
            DB::raw('time'),
            'trailer',
            'driver',
            DB::raw("
                case
                    when state = 'Move' then max(consumption)
                    else null
                end drive_consumption,
                case
                    when state = 'Stop' then max(consumption)
                    else null
                end idle_consumption"),
            'state',
            DB::raw('time::date work_day')
        )
            ->groupBy(
                'id',
                'time',
                'trailer',
                'state',
                'driver'
            );

        $currentFarmYear = FarmingYear::getAll()->where('default', true)->pluck('year')->first();
        $leftJoinPlotsQuery = Plot::select('gid', 'geom')
            ->join('su_farms as f', function ($join) use ($organizationId) {
                $join->on('f.id', '=', 'su_satellite_plots.farm_id')
                    ->where('f.organization_id', '=', $organizationId);
            })
            ->join('su_satellite_orders_plots_rel as ssopr', 'ssopr.plot_id', '=', 'su_satellite_plots.gid')
            ->join('su_satellite_orders as sso', function ($join) use ($currentFarmYear) {
                $join->on('sso.id', '=', 'ssopr.order_id')
                    ->where('sso.year', '=', $currentFarmYear)
                    ->where('sso.type', '=', 'index')
                    ->whereIn('sso.status', ['processed', 'processing', 'paid', 'no_tile']);
            });

        // identify possible works ouside plot
        $plotsQuery = DB::table('points')->select(
            'points.unit_id',
            'points.geom',
            'points.speed',
            'points.time',
            'points.trailer',
            'points.driver',
            'points.drive_consumption',
            'points.idle_consumption',
            'points.state',
            'points.work_day',
            'af.geom as plot',
            'af.gid as plot_id'
        )
            ->leftJoinSub($leftJoinPlotsQuery, 'af', DB::raw('st_intersects(af.geom, points.geom)'), DB::raw('true'));

        $queryGRP = DB::table('plots')->select(
            'unit_id',
            'geom',
            'speed',
            'time',
            'trailer',
            'driver',
            'drive_consumption',
            'idle_consumption',
            'state',
            'work_day',
            'plot',
            'plot_id',
            // check whether previous point not null
            DB::raw('
                case
                    when lag(geom, 1) over (partition by work_day, unit_id order by unit_id, time ) notnull
                    then st_makeline(geom, lag(geom, 1) over ( partition by work_day, unit_id order by unit_id, time))
                    else null
                end lines
                    '),
            DB::raw('lag(time, 1) over (partition by unit_id order by unit_id, time) start_time'),
            'time as end_time',
            DB::raw("
                case
                    when State = 'Move' then age(time, lag(time, 1) over (partition by work_day, unit_id order by unit_id, time))
                    else null
                end duration
                "),
            DB::raw("
                case
                    when State = 'Stop' then age(time, lag(time, 1) over (partition by work_day, unit_id order by unit_id, time))
                    else null
                end idle_duration
                "),
            // calculate interval between entering in same plot and make gap if more than 30 minutes
            DB::raw("
                case
                    when plot notnull
                    and lag(unit_id, 1) over (partition by unit_id, driver, trailer, plot_id order by unit_id, time)= unit_id
                    and time- lag(time, 1) over (partition by work_day, unit_id, driver, trailer, plot_id order by unit_id, time) > interval '30 minutes'
                    then 1
                end as gap
                "),
            DB::raw('
                case
                    when plot_id is null
                    and trailer notnull then ST_ClusterDBSCAN(geom,
                    eps := 100,
                    minpoints := 10) over (partition by work_day, unit_id order by work_day, unit_id , time )
                end as cluster_id
                ')
        );

        $queryIntersectedGroup = DB::table('grp')->select(
            'unit_id',
            'geom',
            'speed',
            'time',
            'trailer',
            'driver',
            'drive_consumption',
            'idle_consumption',
            'state',
            'work_day',
            'plot',
            'plot_id',
            'lines',
            'start_time',
            'end_time',
            'duration',
            'idle_duration',
            'gap',
            'cluster_id',
            DB::raw('
            case
                when cluster_id notnull then 1
                else null
            end is_cluster
            '),
            // cluster points to find work without plot
            DB::raw(
                'count (gap) over (partition by work_day, unit_id
                    order by unit_id, driver, trailer, plot_id, time rows between unbounded preceding and current row ) as groupid'
            )
        )
            // remove empty geoms
            ->whereRaw('ST_IsEmpty(lines) = false');

        $queryTracks = DB::table('intersected_groups')->select(
            'work_day',
            'unit_id',
            'driver',
            'mi.id as trailer_id',
            'mi.width as trailer_width',
            'plot_id',
            'plot',
            DB::raw('max(speed) max_speed'),
            DB::raw('avg(speed) avg_speed'),
            DB::raw('st_linemerge(st_collect(lines)) track'),
            DB::raw('sum(duration) duration'),
            DB::raw('sum(idle_duration) idle_duration'),
            DB::raw('sum(drive_consumption) drive_cons'),
            DB::raw('sum(idle_consumption) idle_cons'),
            DB::raw('min(time) start_time'),
            DB::raw('max(time) end_time'),
            // TODO:: add trailer_type to be not transport or trailer width to be not null - if trailer is not null.
            // work should be at least 3 minutes
            // area of envelop of work should be greater than 3*covered area with 1 m width. This removes tracks caused by constant movement in field.
            DB::raw("
                case
                    when plot_id notnull
                    and st_length(st_collect(lines))>0.7 * st_perimeter(plot)
                    and sum(duration)> interval '5 minutes'
                    and 3 * st_area(st_envelope(st_collect(lines)))> st_length(st_collect(lines)) then 'Work'::proposed_events_types_enum
                    when is_cluster = 1
                    and sum(duration)> interval '5 minutes'
                    and 3 * st_area(st_envelope(st_collect(lines)))> st_length(st_collect(lines))
                    and st_area(st_envelope(st_collect(lines)))* 0.2 < (mi.width / 2) /*replace with HALF of implement width  DONE */
                    * st_length(st_collect(lines)) then 'WorkOutsidePlot'::proposed_events_types_enum
                    else 'Transportation'::proposed_events_types_enum
                end task_type
            ")
        )
            ->leftJoin('su_machines_implements as mi', function ($join) use ($organizationId) {
                $join->on('mi.name', '=', 'trailer')
                    ->where('mi.organization_id', $organizationId);
            })
            ->groupBy(
                'unit_id',
                'driver',
                'mi.id',
                'plot_id',
                'plot',
                'work_day',
                'groupid',
                'is_cluster',
                'mi.width'
            )
            ->havingRaw('sum(duration) notnull or sum(idle_consumption)>1');

        $queryUnion = DB::table('tracks')
            ->withExpression('raw_points', $queryRawPoints)
            ->withExpression('points', $queryPoints)
            ->withExpression('plots', $plotsQuery)
            ->withExpression('grp', $queryGRP)
            ->withExpression('intersected_groups', $queryIntersectedGroup)
            ->withExpression('tracks', $queryTracks)
            ->select(
                DB::raw('null plot_id'),
                DB::raw('unit_id as machine_id'),
                DB::raw('trailer_id as machine_implement_id'),
                DB::raw('work_day as date'),
                DB::raw('min("start_time") start_date'),
                DB::raw('max("end_time") end_date'),
                DB::raw('coalesce(max(max_speed)::int, 0) as max_speed'),
                DB::raw('coalesce(round(avg(avg_speed),0)::int, 0) as avg_speed'),
                DB::raw('round((st_length(st_collect(track)))::numeric,3) as length_track'),
                DB::raw('coalesce(round(sum(drive_cons),0)::int, 0) as fuel_consumed_driving'),
                DB::raw('coalesce(round(sum(idle_cons),0)::int, 0) as fuel_consumed_stay'),
                DB::raw('task_type as type'),
                DB::raw('sum(duration) duration'),
                DB::raw('null geom_cultivated'),
                DB::raw('ST_MakeValid(st_collect(track)) geom_track'),
                DB::raw('sum(idle_duration) duration_stay'),
                DB::raw("'Proposed'::proposed_events_stages_enum as stage"),
                DB::raw('array_agg(DISTINCT swo.id) AS work_operation_ids'),
                'driver'
            )
            ->leftJoin('su_machines_implements_work_operations AS smiwo', 'smiwo.implement_id', '=', 'trailer_id')
            ->leftJoin('su_work_operations AS swo', function ($join) {
                $join->where(function ($query) {
                    $query->where('task_type', MachineEvent::TYPE_WORK_OUTSIDE_PLOT);
                    $query->where(function ($query) {
                        $query->where('swo.id', DB::raw('smiwo.work_operation_id'));
                        $query->orWhere(function ($query) {
                            $query->where('swo.name', WorkOperation::WORK_OPERATION_UNKNOWN);
                            $query->whereNull('smiwo.id');
                        });
                    });
                });
                $join->orWhere(function ($query) {
                    $query->where('task_type', MachineEvent::TYPE_TRANSPORTATION);
                    $query->where('swo.name', WorkOperation::WORK_OPERATION_TRANSPORT);
                });
            })
            ->whereIn('task_type', [MachineEvent::TYPE_TRANSPORTATION, MachineEvent::TYPE_WORK_OUTSIDE_PLOT])
            ->whereNotNull('duration')
            ->groupBy(
                'work_day',
                'unit_id',
                'driver',
                'trailer_id',
                'task_type'
            );

        return DB::table('tracks')
            ->withExpression('raw_points', $queryRawPoints)
            ->withExpression('points', $queryPoints)
            ->withExpression('plots', $plotsQuery)
            ->withExpression('grp', $queryGRP)
            ->withExpression('intersected_groups', $queryIntersectedGroup)
            ->withExpression('tracks', $queryTracks)
            ->select(
                'plot_id',
                DB::raw('unit_id as machine_id'),
                DB::raw('trailer_id as machine_implement_id'),
                DB::raw('work_day as date'),
                DB::raw('min("start_time") start_date'),
                DB::raw('max("end_time") end_date'),
                DB::raw('coalesce(max(max_speed)::int, 0) as max_speed'),
                DB::raw('coalesce(round(avg(avg_speed),0)::int, 0) as avg_speed'),
                DB::raw('round((st_length(st_collect(track)))::numeric,3) as length_track'),
                DB::raw('coalesce(round(sum(drive_cons),0)::int, 0) as fuel_consumed_driving'),
                DB::raw('coalesce(round(sum(idle_cons),0)::int, 0) as fuel_consumed_stay'),
                DB::raw('task_type as type'),
                DB::raw('sum(duration) duration'),
                DB::raw("case when ST_IsEmpty(ST_MakeValid(st_buffer(st_collect(track), (trailer_width / 2), 'endcap=flat join=round'))) = false
                                  then ST_MakeValid(st_buffer(st_collect(track), (trailer_width / 2), 'endcap=flat join=round'))
                                  else null
                              end  geom_cultivated"),
                DB::raw('ST_MakeValid(st_collect(track)) geom_track'),
                DB::raw('sum(idle_duration) duration_stay'),
                DB::raw("'Proposed'::proposed_events_stages_enum as stage"),
                DB::raw('array_agg(DISTINCT swo.id) AS work_operation_ids'),
                'driver'
            )
            ->leftJoin('su_machines_implements_work_operations AS smiwo', 'smiwo.implement_id', '=', 'trailer_id')
            ->leftJoin('su_work_operations AS swo', function ($join) {
                $join->where('swo.id', DB::raw('smiwo.work_operation_id'));
                $join->orWhere(function ($query) {
                    $query->where('swo.name', WorkOperation::WORK_OPERATION_UNKNOWN);
                    $query->whereNull('smiwo.id');
                });
            })
            ->where('task_type', MachineEvent::TYPE_WORK)
            ->whereNotNull('duration')
            ->groupBy(
                'work_day',
                'unit_id',
                'driver',
                'trailer_id',
                'plot_id',
                'plot',
                'task_type',
                'trailer_width'
            )
            ->union($queryUnion);
    }
}
