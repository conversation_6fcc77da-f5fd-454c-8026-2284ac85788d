<?php

namespace App\Services\Machine;

use App\Classes\Machine\MachineUnitTrack\MachineUnitTrackFormatFactory;
use App\Classes\Wialon\WialonErrorCodes;
use App\Exceptions\NotFoundException;
use App\Jobs\UpdateTaskStateToOngoing;
use App\Models\CurrentMachineData;
use App\Models\Integration;
use App\Models\IntegrationReportsTypes;
use App\Models\MachineUnit;
use App\Services\Wialon\ReportService;
use App\Services\Wialon\WialonService;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class MachineUnitService
{
    private $wialonService;
    private $reportService;

    public function __construct(WialonService $wialonService, ReportService $reportService)
    {
        $this->wialonService = $wialonService;
        $this->reportService = $reportService;
    }

    public function syncUnitsByIntegration(Integration $integration): array
    {
        if (Integration::ACTIVE !== $integration->status) {
            throw new NotFoundException('No active integrations found!');
        }

        try {
            $wialonUnits = $this->getUnitsByIntegrationUrlAndToken($integration->integrationAddress->getBaseUrl(), $integration->token);
        } catch (Exception $e) {
            if (
                WialonErrorCodes::TOKEN_USER_NOT_FOUND === $e->getCode()
                || WialonErrorCodes::ACCESS_DENIED === $e->getCode()
            ) {
                $integration->deactivate()->save();
            }

            throw $e;
        }
        // Use wialon_unit_id column as array index
        $wialonUnits = array_column($wialonUnits, null, 'wialon_unit_id');

        // Use wialon_unit_id column as array index.
        $existingMachineUnits = MachineUnit::select(
            'id',
            'organization_id',
            'name',
            'wialon_unit_imei',
            'type',
            'last_communication',
            DB::raw('ST_AsGeoJSON(st_transform(last_position, 4326)) AS last_position_geojson'),
            'wialon_unit_id',
            'integration_id'
        )
            ->where('integration_id', $integration->id)
            ->whereNotNull('wialon_unit_id')
            ->get()
            ->keyBy('wialon_unit_id')
            ->toArray();

        $newMachineUnits = [];

        foreach ($wialonUnits as $wialonUnitId => $wialonUnit) {
            $wialonUnitImei = $wialonUnit['wialon_unit_imei'] ?? null;

            if (!$wialonUnitImei) {
                continue;
            }

            if (isset($existingMachineUnits[$wialonUnitId])) {
                // The unit exists in the db. Update wialon_unit_imei in case it is changed.
                $existingMachineUnits[$wialonUnitId]['wialon_unit_imei'] = $wialonUnitImei;
                $existingMachineUnits[$wialonUnitId]['last_position_geojson'] = json_decode(
                    $existingMachineUnits[$wialonUnitId]['last_position_geojson'],
                    true
                );

                // Update last_communication
                if (isset($wialonUnit['last_communication'])) {
                    $existingMachineUnits[$wialonUnitId]['last_communication'] = $wialonUnit['last_communication'];
                }

                // Update last_position
                if (isset($wialonUnit['last_position'])) {
                    $existingMachineUnits[$wialonUnitId]['last_position'] = $wialonUnit['last_position'];
                }

                $existingMachineUnits[$wialonUnitId]['existing'] = true;

                continue;
            }

            // The unit does not exist in the db.
            $newMachineUnits[] = $wialonUnits[$wialonUnitId];
        }

        $allMachineUnits = array_merge($existingMachineUnits, $newMachineUnits);

        return array_values($allMachineUnits);
    }

    public function getUnitsByIntegrationUrlAndToken(string $url, string $token): array
    {
        $this->wialonService->login($token, $url);

        $flags = 1281; // Units flags: 1 (base flag) + 256 (advanced properties) + 1024 (last message and position)
        $wialonUnitsData = $this->wialonService->searchItems(WialonService::ITEMS_TYPE_UNIT, '*', '*', $flags);
        $wialonUnits = $wialonUnitsData['items'] ?? [];

        $machineUnits = [];
        foreach ($wialonUnits as $wialonUnit) {
            $wialonUnitImei = isset($wialonUnit['uid']) && 0 !== intval($wialonUnit['uid'])
                ? intval($wialonUnit['uid'])
                : null;

            $wialonUnitId = isset($wialonUnit['id']) && 0 !== intval($wialonUnit['id'])
                ? intval($wialonUnit['id'])
                : null;

            if (!$wialonUnitImei || !$wialonUnitId) {
                continue;
            }

            $wialonUnitName = $wialonUnit['nm'] ?? null;
            $wialonUnitLastMessage = $wialonUnit['lmsg'] ?? null;
            $lastCommunication = null;
            $lastPositionGeoJson = null;

            if ($wialonUnitLastMessage && isset($wialonUnitLastMessage['t'])) {
                $dt = new DateTime('now', new DateTimeZone('Europe/Sofia'));
                $dt->setTimestamp($wialonUnitLastMessage['t']);
                $lastCommunication = $dt->format('Y-m-d H:i:s');
            }

            if ($wialonUnitLastMessage && isset($wialonUnitLastMessage['pos'])) {
                $latitude = isset($wialonUnitLastMessage['pos']['y']) && strlen($wialonUnitLastMessage['pos']['y']) > 0 ? $wialonUnitLastMessage['pos']['y'] : null;
                $longitude = isset($wialonUnitLastMessage['pos']['x']) && strlen($wialonUnitLastMessage['pos']['x']) > 0 ? $wialonUnitLastMessage['pos']['x'] : null;

                if (isset($latitude, $longitude)) {
                    $lastPositionGeoJson = [
                        'type' => 'Point',
                        'coordinates' => [$longitude, $latitude],
                    ];
                }
            }

            $machineUnits[] = [
                'id' => null,
                'organization_id' => null,
                'name' => $wialonUnitName,
                'wialon_unit_imei' => $wialonUnitImei,
                'type' => 'Tractor',
                'last_communication' => $lastCommunication,
                'last_position_geojson' => $lastPositionGeoJson,
                'wialon_unit_id' => $wialonUnitId,
                'integration_id' => null,
                'existing' => false,
            ];
        }

        return $machineUnits;
    }

    /**
     * @throws Exception
     */
    public function storeCurrentMachineReport(string $serverName, string $tmpTableName, int $organizationId)
    {
        DB::beginTransaction();

        try {
            $machineUnitsCount = MachineUnit::getByOrganization($organizationId)->count();

            if (0 == $machineUnitsCount) {
                throw new Exception('No machine units found!');
            }

            $reportTmpTableExists = Schema::hasTable($tmpTableName);

            if (!$reportTmpTableExists) {
                return;
            }

            $geoJson = CurrentMachineData::createMachinesCurrentGeoJsonFromReport($tmpTableName, $organizationId)->get()->pluck('geojson')->first();

            CurrentMachineData::updateOrCreate(
                ['organization_id' => $organizationId],
                ['geojson' => $geoJson]
            );

            MachineUnit::updateFromReportTmpTable($tmpTableName);

            DB::statement('DROP TABLE public.' . $tmpTableName);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            DB::statement('DROP TABLE IF EXISTS public.' . $tmpTableName);

            throw $e;
        }

        dispatch(
            new UpdateTaskStateToOngoing($serverName, $organizationId)
        );
    }

    public function getMachineUnitTrack(MachineUnit $machineUnit, int $organizationId, string $from, string $to, string $format)
    {
        $machinesTrackReport = IntegrationReportsTypes::getIntegrationReportsQuery(
            Integration::ACTIVE,
            IntegrationReportsTypes::ON_REQUEST,
            $organizationId,
            'machine_track'
        )->first();

        $reportParams = json_decode($machinesTrackReport->params, true);

        if (!isset($reportParams['exec_params']['interval'])) {
            throw new Exception("'machine_track' report does not contain 'interval' key in execParams!");
        }

        $reportParams['exec_params']['interval'] = [
            'flags' => 0,
            'from' => $from,
            'to' => $to,
        ];

        $reportParams['exec_params']['reportObjectId'] = $machineUnit->wialon_unit_id;

        $reportData = $this->reportService->integrationReport(
            $machinesTrackReport->url,
            $reportParams,
            $machinesTrackReport->id,
            $machinesTrackReport->token
        );

        $reportDataEncoded = json_encode($reportData['content']);

        $machineUnitTrackFormatter = MachineUnitTrackFormatFactory::make($format);

        return $machineUnitTrackFormatter->format($machineUnit->id, $reportDataEncoded);
    }

    /**
     * Delete unit and all related events.
     *
     * @return bool Return true if the unit and the events are deleted successfully otherwise return false
     */
    public function deleteUnitWithEvents(MachineUnit $unit): bool
    {
        DB::beginTransaction();

        try {
            $unit->delete();
        } catch (Exception $e) {
            DB::rollBack();

            return false;
        }
        DB::commit();

        return true;
    }
}
