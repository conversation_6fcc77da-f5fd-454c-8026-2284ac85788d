<?php

namespace App\Jobs;

use App\Services\Machine\MachineUnitService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CurrentMachineReportJob extends AbstractCountryAwareJob implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $tmpTableName;
    private $organizationId;
    private $serverName;

    public function __construct(
        string $serverName,
        string $tmpTableName,
        int $organizationId
    ) {
        $this->serverName = $serverName;
        $this->tmpTableName = $tmpTableName;
        $this->organizationId = $organizationId;
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle()
    {
        app(MachineUnitService::class)->storeCurrentMachineReport(
            $this->getCountryIsoAlpha2Code(),
            $this->tmpTableName,
            $this->organizationId
        );
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
