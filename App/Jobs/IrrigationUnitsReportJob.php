<?php

namespace App\Jobs;

use App\Services\Irrigation\IrrigationUnitService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class IrrigationUnitsReportJob extends AbstractCountryAwareJob implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $serverName;
    private $tmpTableName;
    private $organizationId;

    /**
     * IrrigationPerDay constructor.
     *
     * MachineEventsReport constructor.
     *
     * @param ?int $wialonUnitId
     */
    public function __construct(
        $serverName,
        $tmpTableName,
        $organizationId
    ) {
        $this->serverName = $serverName;
        $this->tmpTableName = $tmpTableName;
        $this->organizationId = $organizationId;
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle()
    {
        app(IrrigationUnitService::class)->storeFormattedIrrigationUnitReport($this->tmpTableName, $this->organizationId);
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
