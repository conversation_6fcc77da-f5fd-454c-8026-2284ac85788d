<?php

namespace App\Models;

/**
 * Class IntegrationReportsTypes.
 *
 * @property string $name
 * @property string $execution
 * @property string $period
 * @property json $params
 * @property string $url
 */
class IntegrationReportsTypes extends BaseModel
{
    public const SCHEDULED = 'scheduled';
    public const ON_REQUEST = 'on_request';

    public const IRRIGATION_PER_DAY = 'irrigation_per_day';
    public const IRRIGATION_UNITS = 'irrigation_units';
    public const MACHINE_EVENTS = 'machine_events';
    public const MACHINES_CURRENT = 'machines_current';
    public const MACHINES_LAST_DATA = 'machines_last_data';
    public const MACHINE_TRACK = 'machine_track';
    public const IMPLEMENTS_SYNC = 'implements_sync';
    public const REPORT_FLAG_BY_DATE = 16777216;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_integration_reports_types';

    /**
     * Get integration reports.
     *
     * @param ?int $organizationId
     * @param ?string $reportName
     */
    public static function getIntegrationReportsQuery(string $status = Integration::ACTIVE, string $execution = IntegrationReportsTypes::SCHEDULED, ?int $organizationId = null, ?string $reportName = null)
    {
        $query = self::select(
            'ir.id',
            'su_integration_reports_types.name',
            'su_integration_reports_types.period',
            'su_integration_reports_types.params',
            'su_integration_reports_types.url',
            'i.token',
            'i.organization_id'
        )
            ->join('su_integrations_reports as ir', 'ir.integration_reports_types_id', '=', 'su_integration_reports_types.id')
            ->join('su_integration as i', 'i.id', '=', 'ir.integration_id')
            ->where('i.status', $status)
            ->where('su_integration_reports_types.execution', $execution);

        if (!is_null($organizationId)) {
            $query->where('i.organization_id', $organizationId);
        }
        if (!is_null($reportName)) {
            $query->where('su_integration_reports_types.name', $reportName);
        }

        return $query;
    }

    public function reports()
    {
        return $this->hasMany('App\Models\IntegrationsReports');
    }
}
