<?php

namespace App\Models;

use App\Casts\PgIntArray;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use MStaack\LaravelPostgis\Eloquent\PostgisTrait;
use phpseclib\Math\BigInteger;
use Staudenmeir\LaravelCte\Query\Builder as CteBuilder;

class MachineEvent extends BaseModel
{
    use PostgisTrait;

    public const TASK = 'task';
    public const APPROVED = 'Approved';
    public const PROPOSED = 'Proposed';
    public const TYPE_WORK = 'Work';
    public const TYPE_TRANSPORTATION = 'Transportation';
    public const TYPE_WORK_OUTSIDE_PLOT = 'WorkOutsidePlot';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machine_events';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'geojson_cultivated' => 'object',
        'geojson_track' => 'object',
        'plot' => 'object',
        'report_json' => 'object',
        'machine_unit' => 'object',
        'machine_implement' => 'object',
        'crop' => 'object',
        'products' => 'array',
        'itemStyle' => 'array',
        'work_operations' => 'array',
        'work_operation_ids' => PgIntArray::class,
    ];

    protected $guarded = [];

    protected $postgisFields = [
        'geom_track',
        'geom_cultivated',
    ];

    protected $postgisTypes = [
        'geom_track' => [
            'geomtype' => 'geometry',
            'srid' => 32635,
        ],
        'geom_cultivated' => [
            'geomtype' => 'geometry',
            'srid' => 32635,
        ],
    ];

    public function plot(): HasOne
    {
        return $this->hasOne(Plot::class, 'gid', 'plot_id');
    }

    public function machineUnit(): HasOne
    {
        return $this->hasOne(MachineUnit::class, 'id', 'machine_id');
    }

    public function machineImplement(): HasOne
    {
        return $this->hasOne(MachineImplement::class, 'id', 'machine_implement_id');
    }

    public function workOperations(): Collection
    {
        return WorkOperation::whereIn('id', $this->work_operation_ids ?? [])->get();
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(MachineTask::class, 'machine_event_id', 'id');
    }

    public static function getList(int $organizationId, array $filter, string $lang): LengthAwarePaginator
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = self::getListQuery($organizationId, $filter);
        $query->select(
            'su_machine_events.id',
            DB::raw("
                    CASE WHEN sp.gid NOTNULL THEN
                        JSONB_BUILD_OBJECT(
                            'gid' , sp.gid,
                            'name', sp.name,
                            'area', round((sp.area*{$areaCoef})::numeric, 3),
                            'thumbnail', sp.thumbnail
                        )
                    END as plot
                "),
            DB::raw("
                    CASE WHEN mu.id NOTNULL THEN
                        JSONB_BUILD_OBJECT(
                            'id' , mu.id,
                            'name', mu.name,
                            'type', mu.type
                        )
                    END as machine_unit
                "),
            DB::raw("
                    CASE WHEN mi.id NOTNULL THEN
                        JSONB_BUILD_OBJECT(
                            'id' , mi.id,
                            'name', mi.name,
                            'width', mi.width
                        )
                    END as machine_implement
                "),
            'ewo.work_operations',
            'su_machine_events.date',
            DB::raw('su_machine_events.start_date::time as start_time'),
            DB::raw('su_machine_events.end_date::time as end_time'),
            DB::raw('CASE WHEN NOT ST_IsEmpty(su_machine_events.geom_cultivated) then ST_AsGeoJSON(su_machine_events.geom_cultivated) END as geojson_cultivated'),
            DB::raw('ST_AsGeoJSON(su_machine_events.geom_track) as geojson_track'),
            DB::raw("
                case 
                    when su_machine_events.plot_id isnull then null 
                    when su_machine_events.machine_implement_id notnull 
                        and mi.width > 0 
                        and su_machine_events.geom_cultivated notnull 
                        then ROUND(((ST_Area(su_machine_events.geom_cultivated) / 1000) * {$areaCoef})::numeric, 3)
                    else 0
                end as cultivated_area
                "),
            'su_machine_events.max_speed',
            'su_machine_events.avg_speed',
            DB::raw('round((su_machine_events.length_track::numeric/1000), 1) as length_track'),
            DB::raw('su_machine_events.fuel_consumed_driving::int'),
            DB::raw('su_machine_events.fuel_consumed_stay::int'),
            'su_machine_events.type',
            'su_machine_events.duration',
            'su_machine_events.duration_stay',
            'su_machine_events.stage',
            'su_machine_events.driver',
            'su_machine_events.implement_width'
        )
            ->groupBy(
                'su_machine_events.id',
                'mu.id',
                'mi.id',
                'sp.gid',
                'spc.id',
                'scc.id',
                'ewo.work_operations',
                'f.id'
            )
            ->orderBy(DB::raw('su_machine_events.start_date::date'), 'DESC')
            ->orderBy(DB::raw('mu.id'), 'DESC')
            ->orderBy(DB::raw('su_machine_events.start_date::time'), 'ASC');

        return $query->paginate($filter['limit']);
    }

    /**
     * Get machine events group by types.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data
     */
    public static function getDataByTypeQuery(int $organizationId, array $filter): Builder
    {
        $query = self::getListQuery($organizationId, $filter);

        $query->select(
            'su_machine_events.type',
            DB::raw('count(su_machine_events.type) as value')
        );

        $query->groupBy('su_machine_events.type');

        return $query;
    }

    /**
     * Get work operations from machine events.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data
     */
    public static function getEventsCountByWorkOperationsQuery(int $organizationId, array $filter): Builder
    {
        $eventsCountByImplementQuery = self::getListQuery($organizationId, $filter);
        $eventsCountByImplementQuery->select(
            'ewo.implement_id',
            'ewo.work_operations',
            DB::raw('COUNT(DISTINCT su_machine_events.id) AS events_count'),
        )
            ->groupBy('ewo.implement_id', 'ewo.work_operations');

        return self::from('events_count_by_implement_query')
            ->withExpression('events_count_by_implement_query', $eventsCountByImplementQuery)
            ->select(
                'work_operations',
                DB::raw('SUM(events_count)::int AS value')
            )
            ->groupBy('work_operations');
    }

    /**
     * Get Machine Events data for Tasks BarChart.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data
     *
     * @return Builder
     */
    public static function getDataTasks(int $organizationId, array $filter): array
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $queryMain = self::getListQuery($organizationId, $filter);
        $queryMain->select(
            DB::raw('coalesce(sum(su_machine_events.length_track), 0) as distance'),
            DB::raw('coalesce(sum(round((su_machine_events.fuel_consumed_driving + su_machine_events.fuel_consumed_stay)::numeric, 3)), 0) as fuel'),
            DB::raw("coalesce(sum(round(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric, 3)), 0) AS area"),
            'su_machine_events.date',
            'su_machine_events.stage'
        );
        $queryMain->groupBy('su_machine_events.date', 'su_machine_events.stage');

        $queryCase = DB::table('data_main')
            ->select(
                DB::raw("MAX(CASE WHEN data_main.stage = 'Approved' THEN area else 0 END) as area_approved"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Proposed' THEN area else 0 END) as area_proposed"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Approved' THEN fuel else 0 END) as fuel_approved"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Proposed' THEN fuel else 0 END) as fuel_proposed"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Approved' THEN distance else 0 END) as distance_approved"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Proposed' THEN distance else 0 END) as distance_proposed"),
                DB::raw('SUM(distance) as distance_by_date'),
                DB::raw('SUM(fuel) as fuel_by_date'),
                DB::raw('SUM(area) as area_by_date'),
                DB::raw('(EXTRACT(epoch FROM data_main.date))::BIGINT AS date')
            )
            ->groupBy('data_main.date')
            ->orderBy('data_main.date');

        $queryResult = DB::table('case_data')
            ->withExpression('data_main', $queryMain)
            ->withExpression('case_data', $queryCase)
            ->select(DB::raw("
                json_build_object(
                    'data', json_build_object(
                        'series', json_build_array(
                            json_build_object(
								'name', 'Area (approved)',
								'type', 'bar',
								'stack', 'area',
								'data', coalesce(
                                    json_agg(
                                        json_build_object(
                                            'value', case_data.area_approved,
                                            'itemStyle', json_build_object(
                                                'barBorderRadius', CASE WHEN case_data.area_proposed > 0
                                                    THEN '[0, 0, 0, 0]'::json
                                                    ELSE '[8, 8, 0, 0]'::json
                                                END
                                            )
                                        )
                                    ),
                                    '[]'::json
                                ),
                                'itemStyle', json_build_object(
                                    'color', '#009C9C'
                                )
                            ),
                            json_build_object(
                                    'name', 'Area (proposed)',
                                    'type', 'bar',
                                    'stack', 'area',
                                    'data', coalesce(
                                        json_agg(
                                            json_build_object(
                                                'value', case_data.area_proposed,
                                                'itemStyle', json_build_object(
                                                    'barBorderRadius', '[8, 8, 0, 0]'::json
                                                )
                                            )
                                        ),
                                        '[]'::json
                                    ),
                                    'itemStyle', json_build_object(
                                        'color', '#CCEBEB'
                                    )
                            ),
                            json_build_object(
                                    'name', 'Fuel (approved)',
                                    'type', 'bar',
                                    'stack', 'fuel',
                                    'data', coalesce(
                                        json_agg(
                                            json_build_object(
                                                'value', case_data.fuel_approved,
                                                'itemStyle', json_build_object(
                                                    'barBorderRadius', CASE WHEN case_data.fuel_proposed > 0
                                                        THEN '[0, 0, 0, 0]'::json
                                                        ELSE '[8, 8, 0, 0]'::json
                                                    END
                                                )
                                            )
                                        ),
                                        '[]'::json
                                    ),
                                    'itemStyle', json_build_object(
                                        'color', '#FF8679'
                                    )
                            ),
                            json_build_object(
                                    'name', 'Fuel (proposed)',
                                    'type', 'bar',
                                    'stack', 'fuel',
                                    'data', coalesce(
                                        json_agg(
                                            json_build_object(
                                                'value', case_data.fuel_proposed,
                                                'itemStyle', json_build_object(
                                                    'barBorderRadius', '[8, 8, 0, 0]'::json
                                                )
                                            )
                                        ),
                                        '[]'::json
                                    ),
                                    'itemStyle', json_build_object(
                                        'color', '#FFCFC9'
                                    )
                            ),
                            json_build_object(
                                    'name', 'Distance (approved)',
                                    'type', 'bar',
                                    'stack', 'distance',
                                    'data', coalesce(
                                        json_agg(
                                            json_build_object(
                                                'value', (case_data.distance_approved::float/1000)::int,
                                                'itemStyle', json_build_object(
                                                    'barBorderRadius', CASE WHEN (case_data.distance_proposed::float/1000)::int > 0
                                                        THEN '[0, 0, 0, 0]'::json
                                                        ELSE '[8, 8, 0, 0]'::json
                                                    END
                                                )
                                            )
                                        ),
                                        '[]'::json
                                    ),
                                    'itemStyle', json_build_object(
                                        'color', '#8870BA'
                                    )
                            ),
                            json_build_object(
                                    'name', 'Distance (proposed)',
                                    'type', 'bar',
                                    'stack', 'distance',
                                    'data', coalesce(
                                        json_agg(
                                            json_build_object(
                                                'value', (case_data.distance_proposed::float/1000)::int,
                                                'itemStyle', json_build_object(
                                                    'barBorderRadius', '[8, 8, 0, 0]'::json
                                                )
                                            )
                                        ),
                                        '[]'::json
                                    ),
                                    'itemStyle', json_build_object(
                                        'color', '#AB85E8'
                                    )
                            )
                        ),
                        'xAxis', json_build_array(
                            json_build_object(
                                    'type', 'category',
                                    'data', coalesce(json_agg(case_data.date), '[]'::json)
                            )
                        )
                    ),
                    'totalDistance', coalesce((sum(case_data.distance_by_date)/1000)::int, 0),
		            'totalFuel', coalesce(sum(case_data.fuel_by_date)::int, 0),
		            'totalArea', coalesce(sum(case_data.area_by_date)::int, 0)
                ) as echart_data
            "));

        $echartData = $queryResult->pluck('echart_data')->first();

        return json_decode($echartData, true);
    }

    /**
     * Get machine events.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data
     */
    public static function getListQuery(int $organizationId, array $filter): Builder
    {
        $eventWorkOperationsQuery = self::getEventWorkOperationsQuery($organizationId);

        $query = self::join('su_machine_units AS mu', 'mu.id', '=', 'su_machine_events.machine_id')
            ->leftJoin('su_satellite_plots AS sp', 'sp.gid', '=', 'su_machine_events.plot_id')
            ->leftJoin('su_machines_implements AS mi', 'mi.id', '=', 'su_machine_events.machine_implement_id')
            ->leftJoin('event_work_operations AS ewo', 'ewo.event_id', '=', 'su_machine_events.id')
            ->leftJoin('su_farms AS f', 'f.id', '=', 'sp.farm_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_machine_events.plot_id')
                    ->where(DB::raw('su_machine_events.date::DATE'), '>=', DB::raw('spc.from_date::DATE'))
                    ->where(DB::raw('su_machine_events.date::DATE'), '<=', DB::raw('spc.to_date::DATE'));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'spc.crop_id')
            ->where('mu.organization_id', $organizationId);

        if (isset($filter['from'], $filter['to'])) {
            $eventWorkOperationsQuery->whereRaw(
                'su_machine_events.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date',
                [$filter['from'], $filter['to']]
            );
            $query->whereRaw(
                'su_machine_events.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date',
                [$filter['from'], $filter['to']]
            );
        }

        if (isset($filter['farmIds']) && count($filter['farmIds']) > 0) {
            $query->where(function ($query) use ($filter) {
                $query->whereIn('sp.farm_id', $filter['farmIds'])
                    ->orWhere('su_machine_events.type', 'Transportation');
            });
        }

        if (isset($filter['plotIds']) && count($filter['plotIds']) > 0) {
            $query->whereIn('sp.gid', $filter['plotIds']);
        }

        if (isset($filter['machineIds']) && count($filter['machineIds']) > 0) {
            $eventWorkOperationsQuery->whereIn('su_machine_events.machine_id', $filter['machineIds']);
            $query->whereIn('su_machine_events.machine_id', $filter['machineIds']);
        }

        if (isset($filter['eventIds']) && count($filter['eventIds']) > 0) {
            $eventWorkOperationsQuery->whereIn('su_machine_events.id', $filter['eventIds']);
            $query->whereIn('su_machine_events.id', $filter['eventIds']);
        }

        if (isset($filter['implements']) && count($filter['implements']) > 0) {
            $query->whereIn('mi.name', $filter['implements']);
        }

        $workOperations = $filter['work_operations'] ?? [];

        if (is_array(reset($workOperations))) {
            /*
             * @var $workOperations array[array[int]]
             * example : [[31, 42], [23], ...]
             */
            $query->where(function ($query) use ($workOperations) {
                foreach ($workOperations as $compoundWorkOperation) {
                    $query->orWhere(function ($query) use ($compoundWorkOperation) {
                        $compoundWorkOperationJson = json_encode($compoundWorkOperation);
                        $query->where('ewo.work_operations_ids', '@>', DB::raw("'{$compoundWorkOperationJson}'::JSONB"));
                        $query->where('ewo.work_operations_ids', '<@', DB::raw("'{$compoundWorkOperationJson}'::JSONB"));
                    });
                }
            });
        }

        if (is_int(reset($workOperations))) {
            /**
             * @var $workOperations array[int]
             * example : [31, 42, ...]
             */
            $workOperationsJson = json_encode($workOperations);
            $query->where('ewo.work_operations_ids', '@>', DB::raw("'{$workOperationsJson}'::JSONB"));
        }

        if (isset($filter['types']) && count($filter['types']) > 0) {
            $eventWorkOperationsQuery->whereIn('su_machine_events.type', $filter['types']);
            $query->whereIn('su_machine_events.type', $filter['types']);
        }

        if (isset($filter['stages']) && count($filter['stages']) > 0) {
            $eventWorkOperationsQuery->whereIn('su_machine_events.stage', $filter['stages']);
            $query->whereIn('su_machine_events.stage', $filter['stages']);
        }

        if (isset($filter['driver'])) {
            $eventWorkOperationsQuery->where(DB::raw("COALESCE(su_machine_events.driver, '')"), 'ilike', trim('%' . $filter['driver'] . '%'));
            $query->where(DB::raw("COALESCE(su_machine_events.driver, '')"), 'ilike', trim('%' . $filter['driver'] . '%'));
        }

        if (isset($filter['cropIds']) && count($filter['cropIds']) > 0) {
            $query->whereIn('spc.crop_id', $filter['cropIds']);
        }

        $query->withExpression('event_work_operations', $eventWorkOperationsQuery);

        return $query;
    }

    public static function getEventWorkOperationsQuery(int $organizationId): Builder
    {
        return self::from('su_machine_events')->select(
            'su_machine_events.id AS event_id',
            'su_machine_events.machine_implement_id AS implement_id',
            DB::raw("
                JSONB_AGG(
                    DISTINCT
                        JSONB_BUILD_OBJECT(
                            'id', swo.id,
                            'name', swo.name,
                            'color', swo.color
                        )
                ) AS work_operations
            "),
            DB::raw('
                JSONB_AGG(
                    DISTINCT
                    swo.id
                ) AS work_operations_ids
            '),
            DB::raw('array_agg(distinct swo.name::text) AS work_operations_names')
        )
            ->join('su_machine_units AS smu', 'su_machine_events.machine_id', '=', 'smu.id')
            ->join('su_work_operations AS swo', 'swo.id', '=', DB::raw('ANY(su_machine_events.work_operation_ids)'))
            ->where('smu.organization_id', $organizationId)
            ->groupBy(
                'su_machine_events.id'
            );
    }

    public static function insertMachineEventData(CteBuilder $query): void
    {
        self::insertUsing([
            'plot_id',
            'machine_id',
            'machine_implement_id',
            'date',
            'start_date',
            'end_date',
            'max_speed',
            'avg_speed',
            'length_track',
            'fuel_consumed_driving',
            'fuel_consumed_stay',
            'type',
            'duration',
            'geom_cultivated',
            'geom_track',
            'duration_stay',
            'stage',
            'work_operation_ids',
            'driver',
        ], $query);
    }

    public static function deleteEvents(int $organizationId, string $date, ?int $wialonUnitId)
    {
        $eventsForDelete = self::join('su_machine_units as smu', 'smu.id', '=', 'su_machine_events.machine_id')
            ->where([
                ['su_machine_events.date', $date],
                ['smu.organization_id', $organizationId],
            ]);

        if ($wialonUnitId) {
            $eventsForDelete->where('smu.wialon_unit_id', '=', $wialonUnitId);
        }

        $eventsForDelete->delete();
    }

    public static function detachEventsFromTasks(int $organizationId, string $date, ?int $wialonUnitId)
    {
        $eventsForDetach = self::select('su_machine_events.id as event_id')
            ->join('su_machine_units as smu', 'smu.id', '=', 'su_machine_events.machine_id')
            ->where([
                ['su_machine_events.date', $date],
                ['smu.organization_id', $organizationId],
            ]);

        if ($wialonUnitId) {
            $eventsForDetach->where('smu.wialon_unit_id', '=', $wialonUnitId);
        }

        DB::table('su_machine_tasks')
            ->withExpression('events_for_detach', $eventsForDetach)
            ->join('events_for_detach AS efm', 'efm.event_id', '=', 'su_machine_tasks.machine_event_id')
            ->leftJoin('su_satellite_plots as ssp', 'ssp.gid', '=', 'su_machine_tasks.plot_id')
            ->updateFrom([
                'su_machine_tasks.state' => MachineTask::STATE_PLANNED,
                'su_machine_tasks.covered_area' => DB::raw('ssp.area'),
                'su_machine_tasks.completion_date' => null,
                'su_machine_tasks.machine_event_id' => null,
            ]);
    }

    /**
     * Get all drivers from events.
     *
     * @param ?string $name
     */
    public static function getDrivers(int $organizationId, ?string $name): array
    {
        $query = self::join('su_machine_units AS mu', 'mu.id', '=', 'su_machine_events.machine_id')
            ->where('mu.organization_id', $organizationId)
            ->whereNotNull('su_machine_events.driver')
            ->selectRaw('DISTINCT su_machine_events.driver');

        if (isset($name)) {
            $query->where('su_machine_events.driver', 'ilike', "%{$name}%");
        }

        return $query->pluck('driver')->toArray();
    }

    /**
     * Get all event types (e.g. Work, Transportation, ... etc.).
     */
    public static function getTypes(): array
    {
        $query = self::selectRaw('DISTINCT UNNEST(ENUM_RANGE(NULL::proposed_events_types_enum)) as event_type');

        return $query->pluck('event_type')->toArray();
    }

    /**
     * Calculate the cultivated geom by custom implement width.
     */
    public static function getCultivatedGeomCustom(int $organizationId, int $eventId, float $implementWidth): array
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $recalculatedCultivatedGeomQuery = self::join('su_machine_units AS smu', 'smu.id', '=', 'su_machine_events.machine_id')
            ->where([
                ['smu.organization_id', $organizationId],
                ['su_machine_events.id', $eventId],
            ])
            ->select(
                DB::raw("ST_Buffer(ST_SnapToGrid(su_machine_events.geom_track, 0.1), ({$implementWidth}::float / 2), 'endcap=flat join=round') as geom")
            );

        $query = self::from('recalculated_cultivated_geom')
            ->withExpression('recalculated_cultivated_geom', $recalculatedCultivatedGeomQuery)
            ->select(
                DB::raw("
                    ROUND(
                        ((ST_Area(recalculated_cultivated_geom.geom)/1000)*{$areaCoef})::numeric,
                        3
                    ) AS area
                "),
                DB::raw('
                    CASE WHEN NOT ST_IsEmpty(recalculated_cultivated_geom.geom) THEN
                        ST_AsGeoJSON(
                            ST_MakeValid(
                                recalculated_cultivated_geom.geom
                            )
                        ) 
                    END AS geojson_cultivated
                ')
            );

        return $query->first()->toArray();
    }

    /**
     * Removes all machine events (and its events products) for the specified plots and orders.
     */
    public static function removeByOrderUuidsAndPlotUuids(array $plotUuids, array $orderUuids)
    {
        self::join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'su_machine_events.plot_id')
            ->join('su_satellite_orders_plots_rel AS ssopr', 'ssopr.plot_uuid', '=', 'ssp.uuid')
            ->join('su_satellite_orders AS sso', 'sso.uuid', '=', 'ssopr.order_uuid')
            ->whereIn('ssopr.plot_uuid', $plotUuids)
            ->whereIn('ssopr.order_uuid', $orderUuids)
            ->whereBetween('su_machine_events.date', [DB::raw('sso.from_date'), DB::raw('sso.to_date')])
            ->delete();
    }
}
