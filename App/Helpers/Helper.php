<?php

namespace App\Helpers;

use App;
use App\Exceptions\ForbiddenException;
use Config;
use DateTime;
use DB;
use DOMDocument;
use Exception;
use File;
use Imagick;
use Request;
use View;

class Helper
{
    public static function getSortRequest($defaultSort = '-id')
    {
        $sort = Request::get('sort');

        if ($sort && '' !== trim($sort)) {
            return $sort;
        }

        return $defaultSort;
    }

    public static function getLimitRequest($defaultLimit = 30)
    {
        $limit = Request::get('limit');

        if ($limit && '' !== trim($limit)) {
            return (int)$limit;
        }

        return $defaultLimit;
    }

    public static function parseExtentToOpenLayer($extent = '', $returnFlatArray = false)
    {
        $pattern = '/BOX\(([\-\d]+\.[\d]+) ([\-\d]+\.[\d]+) ?, ?([\-\d]+\.[\d]+) ([\-\d]+\.[\d]+)\)/i';
        $matches = [];
        preg_match($pattern, $extent, $matches);
        $extent = '[' . implode(array_slice($matches, 1), ', ') . ']';

        $result = json_decode($extent);

        if (!$returnFlatArray) {
            $result = array_map(function ($val) {
                return [0 => $val];
            }, $result);
        }

        return $result;
    }

    /**
     * Returns soil color scheme as array.
     *
     * @param string $colorSchemeFile Full path to color scheme file
     * @param string $colorFormat Format of the color (hex|rgb)
     *
     * @return array
     */
    public static function getSoilColorScheme($colorSchemeFile, $colorFormat = 'hex')
    {
        $scheme = [];
        if (!File::exists($colorSchemeFile)) {
            return $scheme;
        }

        $handle = fopen($colorSchemeFile, 'r');

        if (false === $handle) {
            return $scheme;
        }

        $rows = collect([]);
        while (($data = fgetcsv($handle)) !== false) {
            $rows->push($data);
        }

        $rows->each(function ($rowArr) use (&$scheme, $colorFormat) {
            if (isset($rowArr[0], $rowArr[1], $rowArr[2], $rowArr[3])) {
                $valueStr = $rowArr[0];

                if ('rgb' === $colorFormat) {
                    $scheme[$valueStr] = implode(' ', [$rowArr[1], $rowArr[2], $rowArr[3]]);
                } else {
                    $scheme[$valueStr] = '#' . sprintf('%02x', $rowArr[1]) . sprintf('%02x', $rowArr[2]) . sprintf('%02x', $rowArr[3]);
                }
            }
        });

        return $scheme;
    }

    /**
     * Returns soil label scheme as array.
     *
     * @param string $colorSchemeFile Full path to color scheme file
     *
     * @return array
     */
    public static function getSoilLabelScheme($colorSchemeFile)
    {
        $scheme = [];
        if (!File::exists($colorSchemeFile)) {
            return $scheme;
        }

        $handle = fopen($colorSchemeFile, 'r');

        if (false === $handle) {
            return $scheme;
        }

        $rows = collect([]);
        while (($data = fgetcsv($handle)) !== false) {
            $rows->push($data);
        }

        $rows->each(function ($rowArr) use (&$scheme) {
            if (isset($rowArr[4])) {
                $valueStr = $rowArr[0];

                $scheme[$valueStr] = $rowArr[4];
            }
        });

        return $scheme;
    }

    /**
     * Generates random hex color.
     *
     * @return string hex color
     */
    public static function getRandomHexColor()
    {
        $color = dechex(rand(0x000000, 0xFFFFFF));

        return str_pad($color, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Return error as array.
     *
     * @return array|void
     */
    public static function errorResponseMeteoBlue($text)
    {
        return ['error' => $text, 'http_code' => 500];
    }

    /**
     * Create Map Head.
     *
     * @return string hex color
     */
    public static function createMapHeadFile($mapFile)
    {
        $mapStr = View::make('map_head', [
            'wmsserver' => Config::get('globals.WMS_SERVER'),
            'wmssrs' => Config::get('globals.WMS_SRS'),
            'wfsserver' => Config::get('globals.WMS_SERVER'),
            'wfssrs' => Config::get('globals.WMS_SRS'),
            'con' => Config::get('database.connections'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
        ])
            ->render();

        file_put_contents($mapFile, $mapStr);
    }

    /**
     * @throws ForbiddenException
     */
    public static function resolveObject($classPath, array $params = [])
    {
        try {
            return App::makeWith($classPath, $params);
        } catch (Exception $e) {
            throw new ForbiddenException('Class does not exist.');
        }
    }

    /**
     * Converts cyrilic to latin text.
     *
     * @param string text to convert
     *
     * @return string latin text
     */
    public static function convertToLatin($string)
    {
        $cyrilic = ['a', 'b', 'v', 'g', 'd', 'e', 'j', 'z', 'i', 'ii', 'k', 'l', 'm', 'n', 'o', 'p', 'r', 's', 't', 'u', 'f', 'h', 'c', 'ch', 'sh', 'sht', 'u', 'io', 'iu', 'q', 'A', 'B', 'V', 'G', 'D', 'E', 'J', 'Z', 'I', 'II', 'K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'F', 'H', 'C', 'CH', 'SH', 'SHT', 'U', 'IO', 'IU', 'Q'];
        $latin = ['а', 'б', 'в', 'г', 'д', 'е', 'ж', 'з', 'и', 'й', 'к', 'л', 'м', 'н', 'о', 'п', 'р', 'с', 'т', 'у', 'ф', 'х', 'ц', 'ч', 'ш', 'щ', 'ъ', 'ь', 'ю', 'я', 'А', 'Б', 'В', 'Г', 'Д', 'Е', 'Ж', 'З', 'И', 'Й', 'К', 'Л', 'М', 'Н', 'О', 'П', 'Р', 'С', 'Т', 'У', 'Ф', 'Х', 'Ц', 'Ч', 'Ш', 'Щ', 'Ъ', 'Ь', 'Ю', 'Я'];

        return str_replace($latin, $cyrilic, $string);
    }

    /**
     * Checks whether a text is cyrillic or latin.
     *
     * @param string value the text to check
     *
     * @return bool
     */
    public static function isCyrillic(string $value)
    {
        return preg_match('/[А-Яа-яЁё]/u', $value);
    }

    /**
     * Converts a svg string to PNG File.
     *
     * @param string $svg The svg
     * @param string $outFilePath The path for result file
     * @param array $newSize Use to resize the image [width, height]
     *
     * @return null|string Return outFilePath if success otherwise return null
     */
    public static function svgStringToPNGFile(string $svg, string $outFilePath, array $newSize = null)
    {
        $image = new Imagick();
        $image->readImageBlob($svg);
        if ($newSize && 2 === count($newSize)) {
            $image->resizeImage($newSize[0], $newSize[1], Imagick::FILTER_UNDEFINED, 0, true);
        }
        $image->setImageFormat('png');
        $image->writeImage($outFilePath);
        File::delete(glob(sys_get_temp_dir() . '/magick-*')); // Remove temp Imagick files

        if (is_file($outFilePath)) {
            return $outFilePath;
        }

        return;
    }

    /**
     * Has Child Nodes in xml.
     *
     * @param string file
     * @param string nodeName
     *
     * @return bool
     */
    public static function gsHasChildNodes($file, $nodeName)
    {
        $xmlDoc = new DOMDocument();
        $xmlDoc->load($file);

        $domElement = $xmlDoc->documentElement;

        $hasChildNodes = false;
        foreach ($domElement->childNodes as $item) {
            if ($item->nodeName == $nodeName) {
                $hasChildNodes = $item->hasChildNodes();
            }
        }

        return $hasChildNodes;
    }

    /**
     * Filter Meteo Data History.
     *
     * @param array arrContent
     * @param string prevYear
     */
    public static function filterMeteoDataHistory($arrContent, $prevYear)
    {
        if (!is_array($arrContent)) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        $collectContent = collect($arrContent);

        $filtered = $collectContent->filter(function ($item) use ($prevYear) {
            $endDate = date('Y-m-d');
            $startDate = date('Y-m-d', $prevYear);

            return $item['time'] >= $startDate && $item['time'] <= $endDate;
        });

        return $filtered->toArray();
    }

    // NOTE: Probably the same as parseExtentToOpenLayer, check before replacing.
    public static function toOpenLayerFormat($extent)
    {
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);

        return str_replace(',', ' ', $extent);
    }

    public static function vrtToSize($file)
    {
        $dim = [
            'w' => 0,
            'h' => 0,
        ];

        $size = exec('gdalinfo ' . $file . ' | grep "Size is" 2>&1');
        if ($size && preg_match_all('!\d+!', $size, $matches)) {
            $dim['w'] = (int)($matches[0][0] / 10);
            $dim['h'] = (int)($matches[0][1] / 10);
        }

        return $dim;
    }

    public static function vrtToExtent($file)
    {
        $ll = exec('gdalinfo ' . $file . ' | grep "Lower Left"  2>&1');
        $ur = exec('gdalinfo ' . $file . ' | grep "Upper Right"  2>&1');

        if ($ll && $ur) {
            preg_match('#\((.*?)\)#', $ll, $llMatch);
            preg_match('#\((.*?)\)#', $ur, $urMatch);

            if (count($llMatch) && count($urMatch)) {
                $llCoord = $llMatch[1];
                $urCoord = $urMatch[1];

                return preg_replace('/[ ,]+/', ' ', trim($llCoord . $urCoord));
            }
        }

        return '';
    }

    public static function multiUpdate($tableName, $arrForUpdate, $keyName, $columnsToUpdate, $columnCasts = [])
    {
        // Convert object to array
        $arrForUpdate = (array)$arrForUpdate;
        $arrForUpdate = array_map(function ($arrValue) {
            return (array)$arrValue;
        }, $arrForUpdate);

        $columnsCount = count($columnsToUpdate);
        $valuesCount = count($arrForUpdate);

        if (!$valuesCount || !$columnsCount) {
            return;
        }

        $valuesQuery = '';
        foreach ($arrForUpdate as $valueIndex => $value) {
            if (!$value[$keyName]) {
                throw new Exception("Column '{$keyName}' is not set!");
            }

            if (isset($columnCasts[$keyName])) {
                $keyColumnType = $columnCasts[$keyName];
                $value[$keyName] = 'varchar' == $keyColumnType
                    ? "'" . $value[$keyName] . "'"
                    : "'" . $value[$keyName] . "'::{$keyColumnType}";
            }

            $valueQuery = '(' . $value[$keyName] . ', ';

            foreach ($columnsToUpdate as $columnIndex => $column) {
                if (isset($columnCasts[$column])) {
                    $columnType = $columnCasts[$column];
                    $value[$column] = 'varchar' == $columnType
                        ? "'" . $value[$column] . "'"
                        : "'" . $value[$column] . "'::{$columnType}";
                }

                if ($columnIndex != $columnsCount - 1) {
                    $valueQuery .= $value[$column] . ', ';

                    continue;
                }

                $valueQuery .= $value[$column] . ')';
            }

            $valuesQuery .= $valueQuery;
            if ($valueIndex != $valuesCount - 1) {
                $valuesQuery .= ', ';
            }
        }

        $setQuery = '';
        foreach ($columnsToUpdate as $columnIndex => $column) {
            $setQuery .= "\"{$column}\" = c.\"{$column}\"";

            if ($columnIndex != $columnsCount - 1) {
                $setQuery .= ', ';

                continue;
            }
        }

        $sql = 'UPDATE ' . $tableName . ' as t SET ' . $setQuery
            . ' FROM (VALUES ' . $valuesQuery . ') as c("' . $keyName . '", "' . implode('", "', $columnsToUpdate) . '")'
            . ' WHERE c.' . $keyName . ' = t.' . $keyName . '';

        DB::update($sql);
    }

    public static function getRandomUniqueNumbers($min, $max, $quantity)
    {
        $numbers = range($min, $max);
        shuffle($numbers);

        return array_slice($numbers, 0, $quantity);
    }

    /**
     * Convert HEX color format to RGB format.
     */
    public static function convertHexColorToRGB($hex): string
    {
        list($r, $g, $b) = sscanf($hex, '#%02x%02x%02x');

        return "{$r} {$g} {$b}";
    }

    /**
     * Validates date format.
     */
    public static function isValidDate(string $date, string $format = 'Y-m-d'): bool
    {
        $d = DateTime::createFromFormat($format, $date);

        return $d && $d->format($format) == $date;
    }
}
