#!/bin/sh

# Configuration
DB_NAME="susi_main_v5"
CHUNK_SIZE=5000
PG_USER="postgres"    # Adjust as needed
PG_HOST="**************"   # Adjust as needed
PG_PORT="5432"        # Adjust as needed
PG_PASSWORD="6nuk23"        # Set your password here or pass it via environment
LOG_FILE="drop_tmp_tables.log"


# Set PGPASSWORD environment variable
if [ -z "$PG_PASSWORD" ]; then
    echo "ERROR: PG_PASSWORD must be set in the script or as an environment variable."
    exit 1
fi
PGPASSWORD="$PG_PASSWORD"
export PGPASSWORD

# Function to log messages
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo "$1"
}

# Check if psql is installed
if ! command -v psql >/dev/null 2>&1; then
    log "ERROR: PostgreSQL client (psql) not found. Please install it."
    exit 1
fi

# Get total number of tmp_ tables
TOTAL_TABLES=$(psql -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -d "$DB_NAME" -t -c \
    "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'tmp_report_%';" 2>> "$LOG_FILE")

if [ $? -ne 0 ]; then
    log "ERROR: Failed to query table count."
    exit 1
fi

# Remove any whitespace from TOTAL_TABLES
TOTAL_TABLES=$(echo "$TOTAL_TABLES" | tr -d '[:space:]')

log "Total tables to drop: $TOTAL_TABLES"

if [ "$TOTAL_TABLES" -eq 0 ]; then
    log "No tables starting with 'tmp_' found."
    exit 0
fi

# Debug: Log CHUNK_SIZE and TOTAL_TABLES
log "CHUNK_SIZE: $CHUNK_SIZE"
log "SUB_CHUNK_SIZE: $SUB_CHUNK_SIZE"
log "TOTAL_TABLES: $TOTAL_TABLES"

# Calculate number of chunks
CHUNKS=$(expr \( "$TOTAL_TABLES" + "$CHUNK_SIZE" - 1 \) / "$CHUNK_SIZE")
log "Processing in $CHUNKS chunks of $CHUNK_SIZE tables each."

# Loop through chunks using while
OFFSET=0
while [ "$OFFSET" -lt "$TOTAL_TABLES" ]; do
    log "Processing chunk starting at offset $OFFSET..."

    # Fetch table names for the current chunk
    TABLES=$(psql -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -d "$DB_NAME" -t -c \
        "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'tmp_%' LIMIT $CHUNK_SIZE OFFSET $OFFSET;" 2>> "$LOG_FILE")

    if [ $? -ne 0 ]; then
        log "ERROR: Failed to fetch table names at offset $OFFSET."
        exit 1
    fi

    # Check if TABLES is empty
    if [ -z "$TABLES" ]; then
        log "No tables found in this chunk."
        OFFSET=$(expr "$OFFSET" + "$CHUNK_SIZE")
        continue
    fi

    # Process tables in smaller sub-chunks
    echo "$TABLES" | while read -r TABLE_NAME; do
        if [ -n "$TABLE_NAME" ]; then
            # Clean table name
            TABLE_NAME=$(echo "$TABLE_NAME" | sed 's/^[[:space:]]*//; s/[[:space:]]*$//')
            # Quote table name for DROP TABLE
            DROP_STATEMENT="DROP TABLE \"$TABLE_NAME\" CASCADE;"

            # Execute DROP TABLE statement and capture output
            DROP_OUTPUT=$(psql -U "$PG_USER" -h "$PG_HOST" -p "$PG_PORT" -d "$DB_NAME" -c "$DROP_STATEMENT" 2>&1)
            DROP_STATUS=$?

            if [ $DROP_STATUS -eq 0 ]; then
                log "Successfully dropped table $TABLE_NAME."
            else
                log "ERROR: Failed to drop table $TABLE_NAME."
                log "Error message: $DROP_OUTPUT"
                echo "ERROR: Failed to drop table $TABLE_NAME."
                echo "Error message: $DROP_OUTPUT"
                exit 1
            fi
        fi
    done

    # Increment OFFSET
    OFFSET=$(expr "$OFFSET" + "$CHUNK_SIZE")

    # Optional: Add a small delay to avoid overwhelming the database
    sleep 1
done

log "All tmp_ tables dropped successfully."

# Unset PGPASSWORD for security
unset PGPASSWORD